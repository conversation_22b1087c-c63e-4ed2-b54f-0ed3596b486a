use dotenv::dotenv;
use rocket::routes;
use serenity::prelude::*;
use ttx_integration::{ENV, routes};

#[tokio::main]
async fn main() {
    dotenv().ok();

    let token = &ENV.discord_token;

    let intents = GatewayIntents::GUILDS
        | GatewayIntents::GUILD_MESSAGES
        | GatewayIntents::GUILD_MEMBERS
        | GatewayIntents::MESSAGE_CONTENT;

    let mut discord_client = Client::builder(token, intents)
        .await
        .expect("failed to create discord client");

    let rocket_server = rocket::build().mount("/webhook", routes![routes::webhook::handler]);

    let (client_connection_result, server_lunch_result) =
        tokio::join!(discord_client.start(), rocket_server.launch());

    if let Err(why) = client_connection_result {
        println!("error while connecting to discord: {:?}", why);
    }

    if let Err(why) = server_lunch_result {
        println!("error while launching rocket server: {:?}", why);
    }
}
