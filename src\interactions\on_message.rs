use serenity::async_trait;
use serenity::model::channel::{ChannelType, Message};
use serenity::prelude::*;

use crate::ENV;
use crate::tickets::TICKETS;
use crate::trengo::{self, StoreCustomChannelMessageBody};

pub struct Handler;

#[async_trait]
impl EventHandler for Handler {
    async fn message(&self, ctx: Context, msg: Message) {
        if let None = msg.guild_id {
            return;
        }

        if let None = ctx.cache.guild(msg.guild_id.unwrap()) {
            return;
        }

        let guild = ctx.cache.guild(msg.guild_id.unwrap()).unwrap();

        if let None = guild.channels.get(&msg.channel_id) {
            return;
        }

        let channel = guild.channels.get(&msg.channel_id).unwrap();

        if msg.author.bot
            || channel.kind != ChannelType::Text
            || channel.name.chars().all(|c| c.is_numeric())
        {
            return;
        }

        if let Ok(user_id) = channel.name.parse::<u64>() {
            if let Some(ticket) = TICKETS.fetch(user_id) {
                if !msg.content.is_empty() {
                    let body = StoreCustomChannelMessageBody {
                        channel: ENV.trengo_custom_channel_token.clone(),
                        contact_identifier: ticket.contact_identifier.clone(),
                        contact_name: ticket.contact_name.clone(),
                        contact_email: None,
                        content: msg.content,
                        attachments: vec![],
                    };

                    trengo::store_custom_channel_message(body).await;
                }

                for attachment in msg.attachments {
                    // trengo::store_custom_channel_message(StoreCustomChannelMessageBody {
                    //     channel: ENV.trengo_custom_channel_token,
                    //     content: attachment.url,
                    // })
                    // .await;
                }
            }
        }
    }
}
