use reqwest::Response;
use serde::Serialize;

use crate::trengo::error::Error;
use crate::trengo::{BASE_URL, CLIENT};

#[derive(Debug, Serialize)]
pub struct StoreCustomChannelMessageBody {
    pub channel: String,
    pub contact_identifier: String,
    pub contact_name: String,
    pub contact_email: Option<String>,
    pub content: String,
    pub attachments: Vec<Attachment>,
}

#[derive(Debug, Serialize)]
pub struct Attachment {
    name: String,
    url: String,
}

pub async fn store_custom_channel_message(
    body: StoreCustomChannelMessageBody,
) -> Result<Response, Error> {
    CLIENT
        .post(format!("{BASE_URL}/custom_channel_messages").as_str())
        .json(&serde_json::json!({
          "channel": body.channel,
          "contact": {
            "identifier": body.contact_identifier,
            "name": body.contact_name,
            "email": body.contact_email
          },
          "body": {
            "text": body.content
          },
          "attachments": body.attachments
        }))
        .send()
        .await
        .map_err(|e| e.into())
}
