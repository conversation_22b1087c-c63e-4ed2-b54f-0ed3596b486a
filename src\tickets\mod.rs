use std::collections::HashMap;

use once_cell::sync::Lazy;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct Ticket {
    pub id: u64,
    pub user_id: u64,
    pub guild_id: u64,
    pub category_id: u64,
    pub channel_id: u64,
    pub contact_name: String,
    pub contact_identifier: String,
}

pub struct Tickets {
    tickets: HashMap<u64, Ticket>,
}

impl Tickets {
    pub fn new() -> Self {
        Self {
            tickets: HashMap::new(),
        }
    }

    pub fn insert(&mut self, ticket: Ticket) {
        self.tickets.insert(ticket.id, ticket);
    }

    pub fn update(&mut self, ticket: Ticket) {
        let fetched = self.tickets.get_mut(&ticket.id);

        if let Some(fetched) = fetched {
            fetched.guild_id = ticket.guild_id;
            fetched.category_id = ticket.category_id;
            fetched.channel_id = ticket.channel_id;
            fetched.contact_name = ticket.contact_name;
        }
    }

    pub fn fetch(&self, id: u64) -> Option<&Ticket> {
        self.tickets.get(&id)
    }
}

pub static TICKETS: Lazy<Tickets> = Lazy::new(Tickets::new);
